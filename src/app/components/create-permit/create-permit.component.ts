import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators, AbstractControl, ValidationErrors } from '@angular/forms';
import {
  ResultType,
  UnviredCordovaSDK,
} from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import {
  <PERSON><PERSON><PERSON><PERSON>roller,
  LoadingController,
  ModalController,
} from '@ionic/angular';
import {
  AGENT_HEADER,
  DIVISION_HEADER,
  FACILITY_HEADER,
  FORM_HEADER,
  PERMIT_FORM,
  PERMIT_HEADER,
  PERMIT_LOG,
  PERMIT_STAKEHOLDER,
  PERMIT_TYPE_HEADER,
  STRUCTURE_HEADER,
  USER_CONTEXT_HEADER,
} from 'src/app/data-models/data_classes';
import { DataService } from 'src/app/services/data.service';
import * as moment from 'moment';

@Component({
  selector: 'app-create-permit',
  templateUrl: './create-permit.component.html',
  styleUrls: ['./create-permit.component.scss'],
})
export class CreatePermitComponent implements OnInit {
  public createForm: FormGroup;
  public permitTypesList: PERMIT_TYPE_HEADER[] = [];
  public userHeader: USER_CONTEXT_HEADER;
  public facilitiesList: FACILITY_HEADER[] = [];
  public divisionsList: DIVISION_HEADER[] = [];
  public usersList: any[] = [];
  public structuresArray: STRUCTURE_HEADER[] = [];
  public agentsExternalList: AGENT_HEADER[] = [];
  public agentsInternalList: AGENT_HEADER[] = [];
  public userObj: any;
  public errorMessage: string = '';
  public minDate = null;

  // Searchable dropdown properties for Structure Tag
  public isStructureTagDropdownOpen: boolean = false;
  public structureTagSearchTerm: string = '';
  public structureTagDisplayTerm: string = '';
  public filteredStructuresArray: STRUCTURE_HEADER[] = [];
  maxEndDate: Date | null = null;
  minEndDate: Date | null = null;
  constructor(
    public dataService: DataService,
    public unviredSDK: UnviredCordovaSDK,
    public modalController: ModalController,
    public loadingController: LoadingController,
    public alertController: AlertController
  ) {
    this.createForm = new FormGroup({
      permitType: new FormControl('', Validators.required),
      facility: new FormControl('', Validators.required),
      division: new FormControl(
        { value: '', disabled: true },
        Validators.required
      ),
      description: new FormControl('', Validators.required),
      comments: new FormControl(''),
      structureTag: new FormControl(
        { value: '', disabled: true },
        Validators.required
      ),
      agentInternal: new FormControl('', Validators.required),
      agentExternal: new FormControl(''),
      startDate: new FormControl(new Date(), Validators.required),
      endDate: new FormControl(null, [Validators.required, this.endDateValidator.bind(this)]),
      jobNumber: new FormControl(''),


    });
    this.updateEndDateRange(this.createForm.controls['startDate'].value);

    // Listen for start date changes
    this.createForm.get('startDate')?.valueChanges.subscribe(value => {
      if (value) {
        this.updateEndDateRange(value);
        this.createForm.controls['endDate'].setValue(this.getDefaultEndDate(value));
        // Revalidate end date when start date changes
        this.createForm.controls['endDate'].updateValueAndValidity();
      }
    });

    this.createForm.controls['facility'].valueChanges.subscribe((value) => {
      this.createForm.controls['division'].setValue('');
      this.createForm.controls['structureTag'].setValue('');
      this.structureTagSearchTerm = ''; // Clear search term
      this.structureTagDisplayTerm = ''; // Clear display term
      if (value) {
        this.createForm.controls['division'].enable();
      } else {
        this.createForm.controls['division'].disable();
        this.createForm.controls['structureTag'].disable();
      }
    });
    this.createForm.controls['division'].valueChanges.subscribe((value) => {
      this.createForm.controls['structureTag'].setValue('');
      this.structureTagSearchTerm = ''; // Clear search term
      this.structureTagDisplayTerm = ''; // Clear display term
      if (value) {
        this.createForm.controls['structureTag'].enable();
      } else {
        this.createForm.controls['structureTag'].disable();
      }
    });

    this.createForm.valueChanges.subscribe((value) => {
      this.errorMessage = '';
    });

    // Add click listener to close dropdown when clicking outside
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement;
      const dropdown = target.closest('.searchable-dropdown');
      if (!dropdown && this.isStructureTagDropdownOpen) {
        this.isStructureTagDropdownOpen = false;
      }
    });
  }

  async ngOnInit() {
    await this.getUserDetails();
    await this.getPermitTypes();
    await this.getFacilities();
    await this.getAgentsInternal();
    await this.getAgentsExternal();
    let userAgentResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_CONTEXT_HEADER`
    );

    if (userAgentResult.type == ResultType.success) {
      if (userAgentResult?.data?.length > 0) {
        this.userObj = userAgentResult.data[0];
        this.createForm.controls['facility'].setValue(userAgentResult.data[0].CURRENT_FACILITY);
        this.createForm.controls['facility'].disable();
        await this.getDivisions();
      }
    }
  }

  cancel() {
    this.modalController.dismiss(false);
  }


  setStartDateToNow() {
    const now = new Date();
    const localISOString = this.formatDateToLocalISOString(now);
    this.createForm.controls['startDate'].setValue(localISOString);
  }

  async save() {
    try {
      // Check if form is valid before proceeding
      if (this.createForm.invalid) {
        this.errorMessage = 'Please fix all validation errors before saving';
        // Mark all fields as touched to show validation errors
        Object.keys(this.createForm.controls).forEach(key => {
          this.createForm.get(key)?.markAsTouched();
        });
        return;
      }

      await this.displayPleaseWaitLoader('Please wait, Creating Permit');
      if (!this.createForm.controls['agentInternal'].value && !this.createForm.controls['agentExternal'].value) {
        this.errorMessage = 'Please select at least one agent to proceed'
        this.loadingController.dismiss();
      } else {
        let permitHolder = new PERMIT_HEADER();
        permitHolder.PERMIT_NO =
          this.createForm.controls['permitType'].value +
          this.unviredSDK.guid().replace(/-/g, '').slice(0, 8);
        permitHolder.PERMIT_TYPE = this.createForm.controls['permitType'].value;
        permitHolder.DESCRIPTION = this.createForm.controls['description'].value;
        permitHolder.FACILITY_ID = this.createForm.controls['facility'].value;
        permitHolder.DIVISION_ID = this.createForm.controls['division'].value;
        permitHolder.TAG = this.createForm.controls['structureTag'].value;
        permitHolder.AGENT_ID_INT = this.createForm.controls['agentInternal'].value;
        permitHolder.AGENT_ID_EXT = this.createForm.controls['agentExternal'].value;
        permitHolder.JOB_NO = this.createForm.controls['jobNumber'].value;
        permitHolder.STATUS = 'OPEN';
        permitHolder.REQUESTED_BY = `${this.userObj.FIRST_NAME} ${this.userObj.LAST_NAME}`;
        permitHolder.REQUESTED_ON = moment().valueOf();
        permitHolder.PERMIT_DATE = moment(this.createForm.controls['startDate'].value).valueOf();
        permitHolder.EXPIRY_DATE = moment(this.createForm.controls['endDate'].value).valueOf();

        permitHolder.IS_EXTENDED = 'false';
        permitHolder.COMMENTS = this.createForm.controls['comments'].value;
        permitHolder.P_MODE = 'A';
        permitHolder.LID = this.unviredSDK.guid().replace(/-/g, '');
        await this.unviredSDK.dbInsert('PERMIT_HEADER', permitHolder, true);

        let stakeHolder = new PERMIT_STAKEHOLDER();
        stakeHolder.P_MODE = 'A';
        stakeHolder.USER_ID = `${this.userObj.USER_ID}`;
        stakeHolder.ROLE = 'REQUEST';
        stakeHolder.PERMIT_NO = permitHolder.PERMIT_NO;
        stakeHolder.SYNC_STATUS = 0;
        stakeHolder.OBJECT_STATUS = 1;
        stakeHolder.LID = this.unviredSDK.guid().replace(/-/g, '');
        stakeHolder.FID = permitHolder.LID;
        stakeHolder.COMMENT = '';
        stakeHolder.AGENT_ID = !this.createForm.controls['agentInternal'].value ? this.createForm.controls['agentExternal'].value : this.createForm.controls['agentInternal'].value;
        stakeHolder.ROW_ID = '1';

        await this.unviredSDK.dbInsert('PERMIT_STAKEHOLDER', stakeHolder, false);

        let permitLog = new PERMIT_LOG();
        permitLog.LOG_NO = await this.findMaxLogNumberFromDb(
          permitHolder.PERMIT_NO
        );
        permitLog.LID = this.unviredSDK.guid().replace(/-/g, '');
        permitLog.FID = permitHolder.LID;
        permitLog.SYNC_STATUS = 0;
        permitLog.OBJECT_STATUS = 1;
        permitLog.CREATED_ON = new Date().getTime();
        permitLog.P_MODE = 'A';
        permitLog.PERMIT_NO = permitHolder.PERMIT_NO;
        permitLog.PERMIT_STATUS = 'OPEN';
        permitLog.CREATED_BY = `${this.userObj.USER_ID}`;
        permitLog.COMMENT = `${permitLog.CREATED_BY
          } created permit on ${moment().format('MMMM Do YYYY, h:mm:ss a')}`;
        permitLog.ACTION = 'TRANSITION';
        await this.unviredSDK.dbInsert('PERMIT_LOG', permitLog, false);

        let permitform = new PERMIT_FORM();
        let formdetails = await this.getPermitForm(permitHolder.PERMIT_TYPE);
        if(formdetails != undefined){
          permitform.FORM_ID = formdetails.FORM_ID;
          permitform.DATA = "{}";
          permitform.CHANGED_BY = `${this.userObj.USER_ID}`;
          permitform.CHANGED_ON = new Date().getTime();
          permitform.COMPLETED = ''
          permitform.FID = permitHolder.LID;
          permitform.FORM_GUID = this.unviredSDK.guid().replace(/-/g, '');
          permitform.FORM_NAME = formdetails.FORM_NAME
          permitform.FORM_TITLE = formdetails.FORM_TITLE
          permitform.FORM_VERSION = formdetails.FORM_VERSION
          permitform.LID = this.unviredSDK.guid().replace(/-/g, '');
          permitform.OBJECT_STATUS = 2
          permitform.PARTIAL_FLAG = ''
          permitform.PERMIT_NO = permitHolder.PERMIT_NO
          permitform.P_MODE = 'A'

          await this.unviredSDK.dbInsert('PERMIT_FORM', permitform, false);
        }

        // Remove display-only properties before sending to server
        if ((permitHolder as any).hasOwnProperty('isShowDetailsButton')) {
          delete (permitHolder as any).isShowDetailsButton;
        }
        if ((permitHolder as any).hasOwnProperty('isShowReviseButton')) {
          delete (permitHolder as any).isShowReviseButton;
        }
        // Remove permitTypeInfo as it's only used for display purposes and should not be sent to server
        if ((permitHolder as any).hasOwnProperty('permitTypeInfo')) {
          delete (permitHolder as any).permitTypeInfo;
        }

        let permitHeaderResponse = await this.dataService.modifyPermit(
          permitHolder
        );

        let infoMsg = this.dataService.handleInfoMessage(permitHeaderResponse);
        if (permitHeaderResponse.type == ResultType.success) {
          this.loadingController.dismiss();
          if (infoMsg && infoMsg?.length > 0) {
            this.errorMessage = infoMsg
          } else {
            this.modalController.dismiss(true);
          }
        } else {
          this.loadingController.dismiss();
          if (
            permitHeaderResponse.message &&
            permitHeaderResponse.message.length > 0
          ) {
            this.errorMessage = permitHeaderResponse.message;
          } else if (
            permitHeaderResponse.error &&
            permitHeaderResponse.error.length > 0
          ) {
            this.errorMessage = permitHeaderResponse.error
          } else {
            this.errorMessage = 'Error occured while saving permit, Please try again'
          }
        }
      }
    } catch (err: any) {
      this.loadingController.dismiss();
      this.dataService.showAlertMessage(
        'Error',
        'Error While creating permit ' + err
      );
    }
  }

  async showAlert(title: string, message: string) {
    const alert = await this.alertController.create({
      header: title,
      message: message,
      buttons: ['OK'],
    });
    await alert.present();
  }

  async findMaxLogNumberFromDb(PERMIT_NO: any) {
    let maxNumber = 1;
    let fetchMaxLogNumberQuery = `SELECT MAX(LOG_NO) as maxNumber FROM PERMIT_LOG WHERE PERMIT_NO = '${PERMIT_NO}'`;
    let maxLogNumberResult: any = await this.unviredSDK.dbExecuteStatement(
      fetchMaxLogNumberQuery
    );
    if (
      maxLogNumberResult &&
      maxLogNumberResult.data.length > 0 &&
      maxLogNumberResult.data[0].maxNumber
    ) {
      maxNumber = maxLogNumberResult.data[0].maxNumber + 1;
    }
    return maxNumber;
  }

  async getPermitTypes() {
    let headerName = 'PERMIT_TYPE_HEADER';
    let fetchMasterDataQuery = `SELECT * FROM ${headerName} ORDER BY PERMIT_TYPE ASC`;
    await this.unviredSDK
      .dbExecuteStatement(fetchMasterDataQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            this.permitTypesList = result.data;
          }
        }
      });
  }

  async getAgentsInternal() {
    let headerName = 'AGENT_HEADER';
    let getAgentsExternalQuery = `SELECT * FROM ${headerName} WHERE IS_INTERNAL = 'true' AND IS_ACTIVE = 'true' ORDER BY NAME ASC`;
    await this.unviredSDK
      .dbExecuteStatement(getAgentsExternalQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            this.agentsInternalList = result.data;
          }
        }
      });
  }

  async getAgentsExternal() {
    let headerName = 'AGENT_HEADER';
    let getAgentsExternalQuery = `SELECT * FROM ${headerName} WHERE IS_INTERNAL = 'false' AND IS_ACTIVE = 'true' ORDER BY NAME ASC`;
    await this.unviredSDK
      .dbExecuteStatement(getAgentsExternalQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            this.agentsExternalList = result.data;
          }
        }
      });
  }

  async getFacilities() {
    let headerName = 'FACILITY_HEADER';
    let getFacilitiesQuery = `SELECT * FROM ${headerName} ORDER BY NAME ASC`;
    await this.unviredSDK
      .dbExecuteStatement(getFacilitiesQuery)
      .then(async (result: any) => {
        if (result.type == ResultType.success) {
          if (result.data.length > 0) {
            this.facilitiesList = result.data;
          } else {
            await this.dataService.getFacility();
            let headerName = 'FACILITY_HEADER';
            let getFacilitiesQuery = `SELECT * FROM ${headerName} ORDER BY NAME ASC`;
            await this.unviredSDK
              .dbExecuteStatement(getFacilitiesQuery)
              .then(async (result: any) => {
                if (result.type == ResultType.success) {
                  if (result.data.length > 0) {
                    this.facilitiesList = result.data;
                  }
                }
              })
          }
        }
      })
  }

  async getUserDetails() {
    let userAgentResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT * FROM USER_CONTEXT_HEADER`
    );
    if (userAgentResult.type == ResultType.success) {
      if (userAgentResult?.data?.length > 0) {
        this.userHeader = userAgentResult.data[0];
      }
    }
  }
  async getDivisions() {
    // await this.displayPleaseWaitLoader('Please wait, Loading Divisions');
    let res = await this.dataService.getData(
      'DIVISION_HEADER',
      `FACILITY_ID='${this.createForm.controls['facility'].value}'`
    );
    // console.log("Divisions from Db", res);
    if (res.length > 0) {
      this.divisionsList = res;
      this.loadingController.dismiss();
    } else {
      await this.getDivisionsFromServer(
        this.createForm.controls['facility'].value
      );
      this.loadingController.dismiss();
    }
  }
  //Get Divisions From Server
  async getDivisionsFromServer(facility) {
    this.divisionsList = [];
    let res: any;
    let customData = {
      DIVISION: [
        {
          DIVISION_HEADER: {
            FACILITY_ID: facility,
            DIVISION_ID: '',
            NAME: '',
            P_MODE: '',
          },
        },
      ],
    };
    res = await this.dataService.getDivisions(customData);
    res = await this.dataService.getData(
      'DIVISION_HEADER',
      `FACILITY_ID='${facility}'`
    );
    // console.log("Divisions from Db", res);
    if (res.length > 0) {
      this.divisionsList = res;
      this.loadingController.dismiss();
    } else {
      this.loadingController.dismiss();
    }
  }

  async getStructureTags() {
    this.structuresArray = [];
    let res = await this.dataService.getData(
      'STRUCTURE_HEADER',
      `FACILITY_ID='${this.createForm.controls['facility'].value}' AND DIVISION_ID='${this.createForm.controls['division'].value}'`
    );
    if (res.length > 0) {
      this.structuresArray = res;
      this.filteredStructuresArray = [...this.structuresArray]; // Initialize filtered list
      await this.getStructures(
        this.createForm.controls['facility'].value,
        this.createForm.controls['division'].value
      );
    } else {
      await this.getStructuresFromServer();
    }
    // Update display term if a structure tag is already selected
    this.updateStructureTagDisplayTerm();
  }

  async getStructuresFromServer() {
    this.structuresArray = [];
    let res: any;
    let customData = {
      STRUCTURE: [
        {
          STRUCTURE_HEADER: {
            FACILITY_ID: this.createForm.controls['facility'].value,
            DIVISION_ID: this.createForm.controls['division'].value,
            TAG: '',
            NAME: '',
            CATEGORY: '',
            STRUCT_TYPE: '',
            STATUS: '',
            P_MODE: '',
          },
        },
      ],
    };
    res = await this.dataService.getStructures(customData);
    res = await this.dataService.getData(
      'STRUCTURE_HEADER',
      `FACILITY_ID='${this.createForm.controls['facility'].value}' AND DIVISION_ID='${this.createForm.controls['division'].value}'`
    );
    if (res.length > 0) {
      this.structuresArray = res;
      this.filteredStructuresArray = [...this.structuresArray]; // Initialize filtered list
      res = await this.dataService.getStructureTypes();
      res = await this.dataService.getStructureCategories();
      await this.getStructures(
        this.createForm.controls['facility'].value,
        this.createForm.controls['division'].value
      );
    }
    // Update display term if a structure tag is already selected
    this.updateStructureTagDisplayTerm();
  }

  async getStructures(facilityId, divisionId) {
    let res = await this.dataService
      .executeQuery(`SELECT STRUCTURE_HEADER.FACILITY_ID, STRUCTURE_HEADER.DIVISION_ID, STRUCTURE_HEADER.CATEGORY, STRUCTURE_HEADER.NAME, STRUCTURE_HEADER.TAG,STRUCTURE_HEADER.STATUS,
    STRUCTURE_HEADER.STRUCT_TYPE, STRUCTURE_TYPE_HEADER.DESCRIPTION AS TYPE_DESCRIPTION, STRUCTURE_CAT_HEADER.DESCRIPTION AS CAT_DESCRIPTION FROM STRUCTURE_HEADER LEFT
   JOIN STRUCTURE_TYPE_HEADER ON STRUCTURE_HEADER.STRUCT_TYPE= STRUCTURE_TYPE_HEADER.STRUCT_TYPE
   LEFT JOIN STRUCTURE_CAT_HEADER ON STRUCTURE_HEADER.CATEGORY=STRUCTURE_CAT_HEADER.CATEGORY WHERE STRUCTURE_HEADER.FACILITY_ID = '${facilityId}' AND STRUCTURE_HEADER.DIVISION_ID = '${divisionId}' ORDER BY STRUCTURE_HEADER.TAG ASC`);
    if (res.length > 0) {
      this.structuresArray = res;
      this.filteredStructuresArray = [...this.structuresArray]; // Initialize filtered list
    }
    // Update display term if a structure tag is already selected
    this.updateStructureTagDisplayTerm();
    return;
  }

  // Display Loading dialog.
  async displayPleaseWaitLoader(messageReceived) {
    const loading = await this.loadingController.create({
      message: messageReceived,
      backdropDismiss: false,
    });
    await loading.present();
  }

  async divisionChange() {
    await this.getStructureTags();
  }

  async getUsers() {
    this.usersList = [];
    let getUsersQuery = '';
    if (
      this.createForm.controls['agentExternal'].value != null &&
      this.createForm.controls['agentInternal'].value != null
    ) {
      getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,REQUEST from role_header as A join User_header as B on A.role_name=B.role_name and B.User_id in
          (select user_id from agent_user_header where agent_id=(select agent_id from agent_header where agent_id in ('${this.createForm.controls['agentExternal'].value}','${this.createForm.controls['agentInternal'].value}')))
          where REQUEST ='true'`;
    } else if (
      this.createForm.controls['agentExternal'].value != null &&
      this.createForm.controls['agentInternal'].value == null
    ) {
      getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,REQUEST from role_header as A join User_header as B on A.role_name=B.role_name and B.User_id in
          (select user_id from agent_user_header where agent_id=(select agent_id from agent_header where agent_id in ('${this.createForm.controls['agentExternal'].value}')))
          where REQUEST ='true'`;
    } else {
      getUsersQuery = `select B.first_name,B.last_name,B.USER_ID,B.role_name,REQUEST from role_header as A join User_header as B on A.role_name=B.role_name and B.User_id in
          (select user_id from agent_user_header where agent_id=(select agent_id from agent_header where agent_id in ('${this.createForm.controls['agentInternal'].value}')))
          where REQUEST ='true'`;
    }

    let fetchApproveUsersQueryResult = await this.unviredSDK.dbExecuteStatement(
      getUsersQuery
    );
    if (fetchApproveUsersQueryResult?.data?.length > 0) {
      this.usersList = fetchApproveUsersQueryResult?.data;
    }
  }
  async getPermitForm(permittype) {
    console.log("getPermitForm called");
    let formdetails: FORM_HEADER;
    let permitTypeResult = await this.unviredSDK.dbExecuteStatement(
      `SELECT FORM_ID FROM PERMIT_TYPE_HEADER WHERE PERMIT_TYPE = '${permittype}'`
    );
    if (permitTypeResult.type == ResultType.success) {
      if (permitTypeResult?.data?.length > 0) {
        let getFormsResp: any = await this.dataService.getFormTemplates();
        if (getFormsResp.type == ResultType.success) {
          let formDB: FORM_HEADER[] = getFormsResp.data;
          if (formDB && formDB['FORM'] && formDB['FORM'].length > 0) {
            for (let i = 0; i < formDB['FORM'].length; i++) {
              let formHeader: FORM_HEADER = formDB['FORM'][i].FORM_HEADER;
              if (formHeader && permitTypeResult.data[0].FORM_ID === formHeader.FORM_ID) {
                formdetails = formHeader

              }
            }
          }
        }
        // let formResult = await this.unviredSDK.dbExecuteStatement(
        //   `SELECT * FROM FORM_HEADER WHERE FORM_ID = '${permitTypeResult.data[0]}'`
        // );
        // if (formResult.type == ResultType.success) {
        //   if (formResult?.data?.length > 0) {
        //     formdetails = formResult.data[0];
        //   }
        // }
      }
    }
    return formdetails;
  }


  onStartDateChange(_event: any): void {
    const startDate = this.createForm.controls['startDate'].value;
    if (startDate) {
      this.updateEndDateRange(startDate);
      this.createForm.controls['endDate'].setValue(this.getDefaultEndDate(startDate)); // Set default end date (8 hours later)
    } else {
      this.minEndDate = null;
      this.maxEndDate = null;
    }
  }

  updateEndDateRange(startDate: Date): void {
    if (!startDate || isNaN(startDate.getTime())) {
      console.error("Invalid startDate provided:", startDate);
      this.minEndDate = null;
      this.maxEndDate = null;
      return;
    }

    // Set minimum to start date
    this.minEndDate = new Date(startDate);

    // Set maximum to exactly 8 hours from start date
    this.maxEndDate = new Date(startDate.getTime() + 8 * 60 * 60 * 1000);

    console.log("Date range updated:", {
      startDate: startDate,
      minEndDate: this.minEndDate,
      maxEndDate: this.maxEndDate
    });
  }

  formatDateToLocalISOString(date: Date): string {
    const offset = date.getTimezoneOffset() * 60000;
    return new Date(date.getTime() - offset).toISOString().slice(0, -1);
  }

  getDefaultEndDate(startDate: Date): Date {
    const endDate = new Date(startDate);
    endDate.setHours(startDate.getHours() + 8);
    return endDate;
  }

  onEndDateChange(_event: any): void {
    const endDate = this.createForm.controls['endDate'].value;
    const startDate = this.createForm.controls['startDate'].value;

    console.log("End date changed:", endDate);

    if (endDate && startDate) {
      const endDateTime = new Date(endDate);
      const startDateTime = new Date(startDate);
      const maxAllowedTime = new Date(startDateTime.getTime() + 8 * 60 * 60 * 1000);

      if (endDateTime > maxAllowedTime) {
        console.warn("End date exceeds 8-hour limit, resetting to maximum allowed time");
        this.createForm.controls['endDate'].setValue(maxAllowedTime);
      }
    }

    // Trigger validation
    this.createForm.controls['endDate'].markAsTouched();
    this.createForm.controls['endDate'].updateValueAndValidity();
  }

  // Custom validator to ensure end date is not more than 8 hours from start date
  endDateValidator(control: AbstractControl): ValidationErrors | null {
    if (!control.value) {
      return null; // Don't validate if no value
    }

    const startDate = this.createForm?.get('startDate')?.value;
    if (!startDate) {
      return null; // Don't validate if no start date
    }

    const endDate = new Date(control.value);
    const startDateTime = new Date(startDate);
    const maxAllowedTime = new Date(startDateTime.getTime() + 8 * 60 * 60 * 1000); // 8 hours later

    if (endDate > maxAllowedTime) {
      return {
        maxDuration: {
          message: 'End date cannot be more than 8 hours from start date',
          maxAllowed: maxAllowedTime,
          actual: endDate
        }
      };
    }

    if (endDate < startDateTime) {
      return {
        minDate: {
          message: 'End date cannot be before start date',
          minAllowed: startDateTime,
          actual: endDate
        }
      };
    }

    return null;
  }

  // Searchable dropdown methods for Structure Tag
  toggleStructureTagDropdown() {
    this.isStructureTagDropdownOpen = !this.isStructureTagDropdownOpen;
    if (this.isStructureTagDropdownOpen) {
      this.structureTagSearchTerm = '';
      this.filteredStructuresArray = [...this.structuresArray];
      // Focus the search input after a short delay
      setTimeout(() => {
        const searchInput = document.querySelector('.dropdown-search input') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  }

  openStructureTagDropdown() {
    if (!this.isStructureTagDropdownOpen) {
      this.isStructureTagDropdownOpen = true;
      this.structureTagSearchTerm = '';
      this.filteredStructuresArray = [...this.structuresArray];
      // Focus the search input after a short delay
      setTimeout(() => {
        const searchInput = document.querySelector('.dropdown-search input') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }, 100);
    }
  }

  filterStructureTags() {
    if (!this.structureTagSearchTerm || this.structureTagSearchTerm.trim() === '') {
      this.filteredStructuresArray = [...this.structuresArray];
    } else {
      const searchTerm = this.structureTagSearchTerm.toLowerCase();
      this.filteredStructuresArray = this.structuresArray.filter(structure =>
        structure.TAG.toLowerCase().includes(searchTerm) ||
        structure.NAME.toLowerCase().includes(searchTerm)
      );
    }
  }

  selectStructureTag(structure: STRUCTURE_HEADER) {
    this.createForm.get('structureTag')?.setValue(structure.TAG);
    this.structureTagDisplayTerm = `${structure.TAG} - ${structure.NAME}`;
    this.isStructureTagDropdownOpen = false;
  }

  updateStructureTagDisplayTerm() {
    const selectedTag = this.createForm.get('structureTag')?.value;
    if (selectedTag && this.structuresArray.length > 0) {
      const selectedStructure = this.structuresArray.find(structure => structure.TAG === selectedTag);
      if (selectedStructure) {
        this.structureTagDisplayTerm = `${selectedStructure.TAG} - ${selectedStructure.NAME}`;
      }
    }
  }

  // Keyboard event handler
  onStructureTagSearchKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      this.isStructureTagDropdownOpen = false;
      event.preventDefault();
    } else if (event.key === 'Enter' && this.filteredStructuresArray.length > 0) {
      this.selectStructureTag(this.filteredStructuresArray[0]);
      event.preventDefault();
    }
  }
}
