ion-toolbar {
    padding-inline: 10px;
    padding-top: 0px;
    padding-bottom: 0px;
    font-size: 1.1rem;
    font-weight: 600;
    letter-spacing: 0.0125em;
  
  }
  
  .refreshButton {
    // --border-color: #868686 !important;
    --border-width: 1px !important;
    --border-radius: 8px !important;
    font-weight: 600 !important;
  }

  .date-control {
    --border-color: var(--ion-color-step-300, #b3b3b3);
    --border-radius: 4px;
    --border-width: 1px;
    --min-height: 43px;
  }

  ion-col {
    margin-top: 10px;
  }

  .textCenter {
    text-align: center;
}

.date-popover-content {
  width: fit-content !important;
}

/* Angular DateTime Picker Custom Styling */
:host ::ng-deep {
  .owl-dt-popup {
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  .owl-dt-container {
    border-radius: 8px;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell {
    border-radius: 4px;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell.owl-dt-calendar-cell-selected {
    background-color: var(--ion-color-primary);
    color: white;
  }

  .owl-dt-calendar-table .owl-dt-calendar-cell.owl-dt-calendar-cell-today {
    border: 2px solid var(--ion-color-primary);
  }

  .owl-dt-timer-box {
    border-radius: 4px;
    border: 1px solid var(--ion-color-step-300, #b3b3b3);
  }

  .owl-dt-control-button {
    background-color: var(--ion-color-primary);
    color: white;
    border-radius: 4px;
    border: none;
    padding: 8px 16px;
    font-weight: 500;
  }

  .owl-dt-control-button:hover {
    background-color: var(--ion-color-primary-shade);
  }

  .owl-dt-container-buttons {
    border-top: 1px solid var(--ion-color-step-200, #e0e0e0);
    padding: 12px 16px;
  }
}

/* Searchable dropdown styling */
.searchable-dropdown-container {
  position: relative;
  width: 100%;
}

.dropdown-label {
  position: absolute;
  top: -8px;
  left: 16px;
  background: white;
  padding: 0 4px;
  font-size: 12px;
  color: #666;
  z-index: 1;
  font-weight: 500;
}

.searchable-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  border: 1px solid var(--ion-color-step-300, #b3b3b3);
  border-radius: 4px;
  background: white;
  min-height: 43px;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.dropdown-input-wrapper:hover {
  border-color: var(--ion-color-primary);
}

.dropdown-open .dropdown-input-wrapper {
  border-color: var(--ion-color-primary);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.dropdown-icon {
  margin-left: 16px;
  margin-right: 8px;
  color: #00629b;
  font-size: 18px;
}

.dropdown-input {
  flex: 1;
  border: none;
  outline: none;
  padding: 12px 8px;
  font-size: 14px;
  background: transparent;
  cursor: pointer;
}

.dropdown-input:focus {
  cursor: text;
}

.dropdown-arrow {
  margin-right: 16px;
  color: #666;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.dropdown-arrow.rotated {
  transform: rotate(180deg);
}

.dropdown-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--ion-color-primary);
  border-top: none;
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.dropdown-search {
  position: sticky;
  top: 0;
  background: white;
  border-bottom: 1px solid #eee;
  padding: 8px;
  display: flex;
  align-items: center;
}

.search-icon {
  color: #666;
  margin-right: 8px;
  font-size: 16px;
}

.dropdown-search input {
  flex: 1;
  border: none;
  outline: none;
  padding: 8px;
  font-size: 14px;
  background: #f8f9fa;
  border-radius: 4px;
}

.dropdown-option {
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.dropdown-option:hover {
  background-color: #f8f9fa;
}

.dropdown-option.selected {
  background-color: var(--ion-color-primary-tint);
  color: var(--ion-color-primary);
  font-weight: 500;
}

.dropdown-option:last-child {
  border-bottom: none;
}

.no-options {
  padding: 16px;
  text-align: center;
  color: #666;
  font-style: italic;
}